{"name": "word-wrap", "description": "Wrap words to a specified length.", "version": "1.2.5", "homepage": "https://github.com/jonschlinkert/word-wrap", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON><PERSON> <<EMAIL>> (localhost:8080)", "<PERSON><PERSON> <<EMAIL>> (https://2fd.github.io)", "<PERSON> <<EMAIL>> (https://twitter.com/hildjj)", "<PERSON> <<EMAIL>> (http://twitter.com/jonschlinkert)", "<PERSON> (https://tck.io)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/lordvlad)", "<PERSON> (http://www.linestarve.com)", "<PERSON> <<EMAIL>> (http://zachhale.com)"], "repository": "jonschlinkert/word-wrap", "bugs": {"url": "https://github.com/jonschlinkert/word-wrap/issues"}, "license": "MIT", "files": ["index.js", "index.d.ts"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "keywords": ["break", "carriage", "line", "new-line", "newline", "return", "soft", "text", "word", "word-wrap", "words", "wrap"], "typings": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["common-words", "shuffle-words", "unique-words", "wordcount"]}, "reflinks": ["verb", "verb-generate-readme"]}}