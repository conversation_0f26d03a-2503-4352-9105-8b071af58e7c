{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\223565_Mufeed_FWD_Final_Lab\\\\client\\\\src\\\\index.js\";\nimport React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport App from \"./App\";\nimport \"./index.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render(/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 13\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/223565_Mufeed_FWD_Final_Lab/client/src/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport ReactDOM from \"react-dom/client\";\r\nimport App from \"./App\";\r\nimport \"./index.css\";\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\r\nroot.render(<App />);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cAACL,OAAA,CAACF,GAAG;EAAAQ,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}