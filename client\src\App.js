import React, { useState, useEffect } from "react";
import axios from "axios";

function App() {
  const [name, setName] = useState("");
  const [message, setMessage] = useState("");
  const [feedbacks, setFeedbacks] = useState([]);

  const fetchFeedbacks = async () => {
    const res = await axios.get("/api/feedback");
    setFeedbacks(res.data);
  };

  useEffect(() => {
    fetchFeedbacks();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    await axios.post("/api/feedback", { name, message });
    setName("");
    setMessage("");
    fetchFeedbacks();
  };

  return (
    <div className="container">
      <h1>Feedback Collector</h1>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          placeholder="Enter your name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
        <textarea
          placeholder="Enter your feedback"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          required
        />
        <button type="submit">Submit Feedback</button>
      </form>

      <hr />
      <h2>All Feedback</h2>
      <ul>
        {feedbacks.map((fb) => (
          <li key={fb._id}>
            <strong>{fb.name}:</strong> {fb.message}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default App;
