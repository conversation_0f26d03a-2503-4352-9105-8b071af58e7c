{"ast": null, "code": "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function (c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function (c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}", "map": {"version": 3, "names": ["m", "require", "process", "env", "NODE_ENV", "exports", "createRoot", "hydrateRoot", "i", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "c", "o", "usingClientEntryPoint", "h"], "sources": ["C:/Users/<USER>/Desktop/223565_Mufeed_FWD_Final_Lab/client/node_modules/react-dom/client.js"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC5B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,OAAO,CAACC,UAAU,GAAGN,CAAC,CAACM,UAAU;EACjCD,OAAO,CAACE,WAAW,GAAGP,CAAC,CAACO,WAAW;AACrC,CAAC,MAAM;EACL,IAAIC,CAAC,GAAGR,CAAC,CAACS,kDAAkD;EAC5DJ,OAAO,CAACC,UAAU,GAAG,UAASI,CAAC,EAAEC,CAAC,EAAE;IAClCH,CAAC,CAACI,qBAAqB,GAAG,IAAI;IAC9B,IAAI;MACF,OAAOZ,CAAC,CAACM,UAAU,CAACI,CAAC,EAAEC,CAAC,CAAC;IAC3B,CAAC,SAAS;MACRH,CAAC,CAACI,qBAAqB,GAAG,KAAK;IACjC;EACF,CAAC;EACDP,OAAO,CAACE,WAAW,GAAG,UAASG,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;IACtCH,CAAC,CAACI,qBAAqB,GAAG,IAAI;IAC9B,IAAI;MACF,OAAOZ,CAAC,CAACO,WAAW,CAACG,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAAC;IAC/B,CAAC,SAAS;MACRH,CAAC,CAACI,qBAAqB,GAAG,KAAK;IACjC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}