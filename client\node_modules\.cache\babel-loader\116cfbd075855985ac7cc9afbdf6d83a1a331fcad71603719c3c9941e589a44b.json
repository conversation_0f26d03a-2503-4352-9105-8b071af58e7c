{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\223565_Mufeed_FWD_Final_Lab\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [name, setName] = useState(\"\");\n  const [message, setMessage] = useState(\"\");\n  const [feedbacks, setFeedbacks] = useState([]);\n  const fetchFeedbacks = async () => {\n    const res = await axios.get(\"/api/feedback\");\n    setFeedbacks(res.data);\n  };\n  useEffect(() => {\n    fetchFeedbacks();\n  }, []);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    await axios.post(\"/api/feedback\", {\n      name,\n      message\n    });\n    setName(\"\");\n    setMessage(\"\");\n    fetchFeedbacks();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Feedback Collector\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Enter your name\",\n        value: name,\n        onChange: e => setName(e.target.value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        placeholder: \"Enter your feedback\",\n        value: message,\n        onChange: e => setMessage(e.target.value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Submit Feedback\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"All Feedback\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: feedbacks.map(fb => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [fb.name, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this), \" \", fb.message]\n      }, fb._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"IC45mB6rS2ppp5vJZyVeXqNQFAY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "App", "_s", "name", "setName", "message", "setMessage", "feedbacks", "setFeedbacks", "fetchFeedbacks", "res", "get", "data", "handleSubmit", "e", "preventDefault", "post", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "map", "fb", "_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/223565_Mufeed_FWD_Final_Lab/client/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\n\r\nfunction App() {\r\n  const [name, setName] = useState(\"\");\r\n  const [message, setMessage] = useState(\"\");\r\n  const [feedbacks, setFeedbacks] = useState([]);\r\n\r\n  const fetchFeedbacks = async () => {\r\n    const res = await axios.get(\"/api/feedback\");\r\n    setFeedbacks(res.data);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchFeedbacks();\r\n  }, []);\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    await axios.post(\"/api/feedback\", { name, message });\r\n    setName(\"\");\r\n    setMessage(\"\");\r\n    fetchFeedbacks();\r\n  };\r\n\r\n  return (\r\n    <div className=\"container\">\r\n      <h1>Feedback Collector</h1>\r\n      <form onSubmit={handleSubmit}>\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Enter your name\"\r\n          value={name}\r\n          onChange={(e) => setName(e.target.value)}\r\n          required\r\n        />\r\n        <textarea\r\n          placeholder=\"Enter your feedback\"\r\n          value={message}\r\n          onChange={(e) => setMessage(e.target.value)}\r\n          required\r\n        />\r\n        <button type=\"submit\">Submit Feedback</button>\r\n      </form>\r\n\r\n      <hr />\r\n      <h2>All Feedback</h2>\r\n      <ul>\r\n        {feedbacks.map((fb) => (\r\n          <li key={fb._id}>\r\n            <strong>{fb.name}:</strong> {fb.message}\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMa,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAMC,GAAG,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,eAAe,CAAC;IAC5CH,YAAY,CAACE,GAAG,CAACE,IAAI,CAAC;EACxB,CAAC;EAEDf,SAAS,CAAC,MAAM;IACdY,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMjB,KAAK,CAACkB,IAAI,CAAC,eAAe,EAAE;MAAEb,IAAI;MAAEE;IAAQ,CAAC,CAAC;IACpDD,OAAO,CAAC,EAAE,CAAC;IACXE,UAAU,CAAC,EAAE,CAAC;IACdG,cAAc,CAAC,CAAC;EAClB,CAAC;EAED,oBACET,OAAA;IAAKiB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBlB,OAAA;MAAAkB,QAAA,EAAI;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3BtB,OAAA;MAAMuB,QAAQ,EAAEV,YAAa;MAAAK,QAAA,gBAC3BlB,OAAA;QACEwB,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,iBAAiB;QAC7BC,KAAK,EAAEvB,IAAK;QACZwB,QAAQ,EAAGb,CAAC,IAAKV,OAAO,CAACU,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE;QACzCG,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFtB,OAAA;QACEyB,WAAW,EAAC,qBAAqB;QACjCC,KAAK,EAAErB,OAAQ;QACfsB,QAAQ,EAAGb,CAAC,IAAKR,UAAU,CAACQ,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE;QAC5CG,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFtB,OAAA;QAAQwB,IAAI,EAAC,QAAQ;QAAAN,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAEPtB,OAAA;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNtB,OAAA;MAAAkB,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrBtB,OAAA;MAAAkB,QAAA,EACGX,SAAS,CAACuB,GAAG,CAAEC,EAAE,iBAChB/B,OAAA;QAAAkB,QAAA,gBACElB,OAAA;UAAAkB,QAAA,GAASa,EAAE,CAAC5B,IAAI,EAAC,GAAC;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACS,EAAE,CAAC1B,OAAO;MAAA,GADhC0B,EAAE,CAACC,GAAG;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV;AAACpB,EAAA,CArDQD,GAAG;AAAAgC,EAAA,GAAHhC,GAAG;AAuDZ,eAAeA,GAAG;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}