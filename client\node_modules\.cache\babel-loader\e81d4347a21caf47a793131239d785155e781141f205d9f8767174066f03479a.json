{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-refresh-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-refresh-runtime.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["C:/Users/<USER>/Desktop/223565_Mufeed_FWD_Final_Lab/client/node_modules/react-refresh/runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-refresh-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-refresh-runtime.development.js');\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,+CAA+C,CAAC;AAC3E,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}